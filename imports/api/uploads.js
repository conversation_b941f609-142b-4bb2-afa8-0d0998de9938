import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';
import { uploadToR2, deleteFromR2, isValidImageType, isValidFileSize } from './r2Storage.js';

if (Meteor.isServer) {
  // Server-side methods for R2 image uploads
  Meteor.methods({
    'images.uploadCoverPhoto': async function(fileData, fileName, contentType) {
      check(fileData, String); // Base64 encoded file data
      check(fileName, String);
      check(contentType, String);

      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to upload images');
      }

      // Validate file type
      if (!isValidImageType(contentType)) {
        throw new Meteor.Error('invalid-file-type', 'Please upload an image file (PNG, JPG, JPEG, GIF, WebP)');
      }

      // Convert base64 to buffer
      const fileBuffer = Buffer.from(fileData, 'base64');

      // Validate file size
      if (!isValidFileSize(fileBuffer.length)) {
        throw new Meteor.Error('file-too-large', 'File size cannot exceed 10MB');
      }

      try {
        // Upload to R2
        const uploadResult = await uploadToR2(fileBuffer, fileName, contentType, this.userId);

        // Save to cover photos collection for history
        await Meteor.call('coverPhotos.insert', uploadResult.key, uploadResult.key);

        // Update user's cover photo (save the R2 key)
        await Meteor.users.updateAsync(this.userId, {
          $set: {
            'profile.coverPhoto': uploadResult.key
          }
        });

        console.log('Cover photo uploaded and updated successfully');
        return {
          success: true,
          key: uploadResult.key,
          url: uploadResult.url
        };
      } catch (error) {
        console.error('Error uploading cover photo:', error);
        throw new Meteor.Error('upload-failed', 'Failed to upload cover photo');
      }
    },

    'images.updateCoverPhoto': async function(imageKey) {
      check(imageKey, String);

      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to update your cover photo');
      }

      // Save to cover photos collection for history
      await Meteor.call('coverPhotos.insert', imageKey, imageKey);

      // Update user's cover photo (save the R2 key)
      await Meteor.users.updateAsync(this.userId, {
        $set: {
          'profile.coverPhoto': imageKey
        }
      });

      console.log('Cover photo updated successfully');
      return { success: true };
    },

    'images.uploadProfilePicture': async function(fileData, fileName, contentType) {
      check(fileData, String); // Base64 encoded file data
      check(fileName, String);
      check(contentType, String);

      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to upload images');
      }

      // Validate file type
      if (!isValidImageType(contentType)) {
        throw new Meteor.Error('invalid-file-type', 'Please upload an image file (PNG, JPG, JPEG, GIF, WebP)');
      }

      // Convert base64 to buffer
      const fileBuffer = Buffer.from(fileData, 'base64');

      // Validate file size
      if (!isValidFileSize(fileBuffer.length)) {
        throw new Meteor.Error('file-too-large', 'File size cannot exceed 10MB');
      }

      try {
        // Upload to R2
        const uploadResult = await uploadToR2(fileBuffer, fileName, contentType, this.userId);

        // Update user's profile picture (save the R2 key)
        await Meteor.users.updateAsync(this.userId, {
          $set: {
            'profile.profilePicture': uploadResult.key
          }
        });

        console.log('Profile picture uploaded and updated successfully');
        return {
          success: true,
          key: uploadResult.key,
          url: uploadResult.url
        };
      } catch (error) {
        console.error('Error uploading profile picture:', error);
        throw new Meteor.Error('upload-failed', 'Failed to upload profile picture');
      }
    },

    'images.remove': async function(r2Key) {
      check(r2Key, String);

      if (!this.userId) {
        throw new Meteor.Error('not-authorized', 'You must be logged in to remove images');
      }

      try {
        // Delete from R2
        await deleteFromR2(r2Key);
        console.log('Image deleted successfully from R2');
        return { success: true };
      } catch (error) {
        console.error('Error deleting image:', error);
        throw new Meteor.Error('delete-failed', 'Failed to delete image');
      }
    }
  });
}
