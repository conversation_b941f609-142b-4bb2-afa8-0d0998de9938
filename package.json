{"name": "meteor-app", "private": true, "scripts": {"start": "MONGO_URL=mongodb://royalia:<EMAIL>/royalia?authSource=royalia meteor run", "dev": "MONGO_URL=mongodb://royalia:<EMAIL>/royalia?authSource=royalia meteor run --port 3001", "test": "meteor test --once --driver-package meteortesting:mocha", "test-app": "TEST_WATCH=1 meteor test --full-app --driver-package meteortesting:mocha", "visualize": "meteor --production --extra-packages bundle-visualizer"}, "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/lib-storage": "^3.842.0", "@babel/runtime": "^7.20.7", "dotenv": "^17.0.1", "meteor-node-stubs": "^1.2.5", "react": "^18.2.0", "react-dom": "^18.2.0"}, "meteor": {"mainModule": {"client": "client/main.jsx", "server": "server/main.js"}, "testModule": "tests/main.js"}}