import { Meteor } from 'meteor/meteor';

// Only run on server
if (Meteor.isServer) {
  const { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } = require('@aws-sdk/client-s3');
  const { Upload } = require('@aws-sdk/lib-storage');
  const crypto = require('crypto');

  // Debug environment variables
  console.log('R2 Environment Variables:');
  console.log('CF_R2_ENDPOINT:', process.env.CF_R2_ENDPOINT);
  console.log('CF_S3_ACCESS_KEY_ID:', process.env.CF_S3_ACCESS_KEY_ID ? 'SET' : 'NOT SET');
  console.log('CF_S3_SECRET_ACCESS_KEY:', process.env.CF_S3_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET');
  console.log('CF_R2_BUCKET:', process.env.CF_R2_BUCKET);
  console.log('CF_R2_PUBLIC_URL:', process.env.CF_R2_PUBLIC_URL);

  // R2 Configuration
  const R2_CONFIG = {
    region: 'auto', // Cloudflare R2 uses 'auto' region
    endpoint: process.env.CF_R2_ENDPOINT,
    credentials: {
      accessKeyId: process.env.CF_S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.CF_S3_SECRET_ACCESS_KEY,
    },
    forcePathStyle: true, // Required for R2
  };

  const BUCKET_NAME = process.env.CF_R2_BUCKET || 'royalia';

  // Initialize S3 client for R2
  const r2Client = new S3Client(R2_CONFIG);

  console.log('R2 client initialized successfully');

  /**
   * Generate a unique filename for uploaded images
   * @param {string} originalName - Original filename
   * @param {string} userId - User ID for organization
   * @returns {string} - Unique filename
   */
  const generateUniqueFilename = (originalName, userId) => {
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(8).toString('hex');
    const extension = originalName.split('.').pop().toLowerCase();
    return `${userId}/${timestamp}-${randomString}.${extension}`;
  };

  /**
   * Get public URL for R2 object
   * @param {string} key - File key in R2
   * @returns {string} - Public URL
   */
  const getR2PublicUrl = (key) => {
    // Use custom public URL if configured, otherwise fall back to pub-*.r2.dev
    const publicUrl = process.env.CF_R2_PUBLIC_URL;

    if (publicUrl) {
      return `${publicUrl}/${key}`;
    }

    // Fallback to pub-*.r2.dev format (requires bucket to be public)
    const endpoint = process.env.CF_R2_ENDPOINT || 'https://06f9145ce021867938f98ef052b3dc23.r2.cloudflarestorage.com';
    const accountHash = endpoint.replace('https://', '').replace('.r2.cloudflarestorage.com', '');

    return `https://pub-${accountHash}.r2.dev/${key}`;
  };

  /**
   * Upload file to Cloudflare R2
   * @param {Buffer} fileBuffer - File buffer
   * @param {string} filename - Filename
   * @param {string} contentType - MIME type
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Upload result with key and url
   */
  const uploadToR2 = async (fileBuffer, filename, contentType, userId) => {
    try {
      const key = generateUniqueFilename(filename, userId);
      
      const uploadParams = {
        Bucket: BUCKET_NAME,
        Key: key,
        Body: fileBuffer,
        ContentType: contentType,
        // R2 doesn't support ACL, public access is configured at bucket level
      };

      const upload = new Upload({
        client: r2Client,
        params: uploadParams,
      });

      const result = await upload.done();
      
      return {
        key: key,
        url: getR2PublicUrl(key),
        location: result.Location,
        etag: result.ETag,
      };
    } catch (error) {
      console.error('Error uploading to R2:', error);
      throw new Meteor.Error('upload-failed', 'Failed to upload file to R2');
    }
  };

  /**
   * Delete file from Cloudflare R2
   * @param {string} key - File key in R2
   * @returns {Promise<void>}
   */
  const deleteFromR2 = async (key) => {
    try {
      const deleteParams = {
        Bucket: BUCKET_NAME,
        Key: key,
      };

      await r2Client.send(new DeleteObjectCommand(deleteParams));
      console.log(`Successfully deleted ${key} from R2`);
    } catch (error) {
      console.error('Error deleting from R2:', error);
      throw new Meteor.Error('delete-failed', 'Failed to delete file from R2');
    }
  };

  /**
   * Check if file exists in R2
   * @param {string} key - File key in R2
   * @returns {Promise<boolean>} - Whether file exists
   */
  const fileExistsInR2 = async (key) => {
    try {
      const params = {
        Bucket: BUCKET_NAME,
        Key: key,
      };

      await r2Client.send(new GetObjectCommand(params));
      return true;
    } catch (error) {
      if (error.name === 'NoSuchKey') {
        return false;
      }
      throw error;
    }
  };

  /**
   * Validate file type for image uploads
   * @param {string} contentType - MIME type
   * @returns {boolean} - Whether file type is allowed
   */
  const isValidImageType = (contentType) => {
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp'
    ];
    return allowedTypes.includes(contentType.toLowerCase());
  };

  /**
   * Validate file size
   * @param {number} size - File size in bytes
   * @returns {boolean} - Whether file size is allowed
   */
  const isValidFileSize = (size) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    return size <= maxSize;
  };

  // Export functions globally for server use
  global.R2Storage = {
    uploadToR2,
    deleteFromR2,
    fileExistsInR2,
    isValidImageType,
    isValidFileSize,
    getR2PublicUrl
  };
}
